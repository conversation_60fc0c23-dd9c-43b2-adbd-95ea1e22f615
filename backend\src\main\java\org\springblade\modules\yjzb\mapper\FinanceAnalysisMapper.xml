<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.yjzb.mapper.FinanceAnalysisMapper">

    <!-- 财务分析结果映射 -->
    <resultMap id="financeAnalysisResultMap" type="org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="query_year" property="queryYear"/>
        <result column="compare_year" property="compareYear"/>
        <result column="start_month" property="startMonth"/>
        <result column="end_month" property="endMonth"/>
        <result column="input_params" property="inputParams"/>
        <result column="workflow_run_id" property="workflowRunId"/>
        <result column="execute_time" property="executeTime"/>
        <result column="execute_status" property="executeStatus"/>
        <result column="result" property="result"/>
        <result column="report_id" property="reportId"/>
        <result column="think_process" property="thinkProcess"/>
        <result column="answer_content" property="answerContent"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 分页查询财务分析数据 -->
    <select id="selectFinanceAnalysisPage" resultType="org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisVO">
        SELECT * FROM yjzb_finance_analysis
        <where>
            <if test="financeAnalysis.name != null and financeAnalysis.name != ''">
                AND name LIKE CONCAT('%', #{financeAnalysis.name}, '%')
            </if>
            <if test="financeAnalysis.type != null and financeAnalysis.type != ''">
                AND type = #{financeAnalysis.type}
            </if>
            <if test="financeAnalysis.queryYear != null">
                AND year = #{financeAnalysis.queryYear}
            </if>
            <if test="financeAnalysis.compareYear != null">
                AND compare_year = #{financeAnalysis.compareYear}
            </if>
            <if test="financeAnalysis.startMonth != null">
                AND start_month = #{financeAnalysis.startMonth}
            </if>
            <if test="financeAnalysis.endMonth != null">
                AND end_month = #{financeAnalysis.endMonth}
            </if>
        </where>
        ORDER BY sort ASC, create_time DESC
    </select>
    
    <!-- 根据组合主键查询唯一记录 -->
    <select id="selectByCompositeKey" resultMap="financeAnalysisResultMap">
        SELECT * FROM yjzb_finance_analysis
        <where>
            <if test="name != null and name != ''">
                AND name = #{name}
            </if>
            <if test="queryYear != null">
                AND query_year = #{queryYear}
            </if>
            <if test="compareYear != null">
                AND compare_year = #{compareYear}
            </if>
            <if test="startMonth != null">
                AND start_month = #{startMonth}
            </if>
            <if test="endMonth != null">
                AND end_month = #{endMonth}
            </if>
        </where>
        LIMIT 1
    </select>

    <!-- 主要经济指标分析 -->
    <select id="selectMainEconomicIndicators" resultType="java.util.HashMap">
        WITH base_data AS (
            SELECT 
                i.code,
                i.name AS indicator_name,
                EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) AS year,
                ROUND(SUM(iv.value) / 10000, 2) AS actual_value,
                -- 从年度预算表获取预算值，优先使用中期调整预算数，否则使用年初预算数
                COALESCE(
                    CASE
                        WHEN iab.midyear_budget IS NOT NULL AND iab.midyear_budget != 0
                        THEN iab.midyear_budget
                        ELSE iab.initial_budget
                    END, 0
                ) AS budget_value
            FROM yjzb_indicator i
            JOIN yjzb_indicator_values iv ON i.id = iv.indicator_id
            LEFT JOIN yjzb_indicator_annual_budget iab ON i.id = iab.indicator_id
                AND iab.year = EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM'))
                AND iab.is_deleted = 0
            WHERE EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) IN (#{params.compareYear}, #{params.queryYear})
              AND EXTRACT(MONTH FROM TO_DATE(iv.period, 'YYYY-MM')) BETWEEN #{params.startMonth} AND #{params.endMonth}
            GROUP BY i.code, i.name, EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')), iab.midyear_budget, iab.initial_budget
        ),

        cigarette_sales_data AS (
            SELECT
                t.year,
                t.total_sales,
                b.current_used
            FROM (
                SELECT
                    EXTRACT(YEAR FROM TO_DATE(period, 'YYYY-MM')) AS year,
                    SUM(sales) AS total_sales
                FROM yjzb_cigarette_sales
                WHERE EXTRACT(YEAR FROM TO_DATE(period, 'YYYY-MM')) IN (2024, 2025)
                  AND EXTRACT(MONTH FROM TO_DATE(period, 'YYYY-MM')) BETWEEN 1 AND 5
                  AND is_deleted = 0
                GROUP BY EXTRACT(YEAR FROM TO_DATE(period, 'YYYY-MM'))
            ) t
            LEFT JOIN yjzb_indicator_annual_budget b
                ON t.year = b.year
                AND b.indicator_name = '卷烟销量'
                AND b.is_deleted = 0
        ),

        base_pivot_data AS (
            SELECT
                code,
                indicator_name,
                MAX(CASE WHEN year = #{params.queryYear} THEN actual_value END) AS v_query_actual,
                MAX(CASE WHEN year = #{params.compareYear} THEN actual_value END) AS v_compare_actual,
                MAX(CASE WHEN year = #{params.queryYear} THEN budget_value END) AS v_query_budget
            FROM base_data
            GROUP BY code, indicator_name
        ),

        pivot_data AS (
            SELECT * FROM base_pivot_data

            UNION ALL

            SELECT
                'juanyan_xiaoshou_shuliang' AS code,
                '卷烟销售数量' AS indicator_name,
                COALESCE((SELECT total_sales FROM cigarette_sales_data WHERE year = 2025), 0) AS v_2025_actual,
                COALESCE((SELECT total_sales FROM cigarette_sales_data WHERE year = 2024), 0) AS v_2024_actual,
                COALESCE((SELECT current_used FROM cigarette_sales_data WHERE year = 2025), 0) AS v_2025_budget
        ),

        calculated AS (
            -- 税利总额
            SELECT
                '税利总额' AS item,
                COALESCE(p.v_query_actual, 0) AS v_query_actual,
                COALESCE(p.v_compare_actual, 0) AS v_compare_actual,
                COALESCE(p.v_query_budget, 0) AS v_query_budget
            FROM pivot_data p
            WHERE p.indicator_name = '税利合计' OR p.code = 'shuilixiangji'

            UNION ALL

            -- 其中：利润总额
            SELECT
                '其中：利润总额',
                COALESCE(p.v_query_actual, 0),
                COALESCE(p.v_compare_actual, 0),
                COALESCE(p.v_query_budget, 0)
            FROM pivot_data p
            WHERE p.code = 'lirun_zonge_shui'

            UNION ALL

            -- 税费（不含所得税） = 税利合计 - 利润总额
            SELECT
                '税费（不含所得税）',
                COALESCE(p1.v_query_actual, 0) - COALESCE(p2.v_query_actual, 0),
                COALESCE(p1.v_compare_actual, 0) - COALESCE(p2.v_compare_actual, 0),
                COALESCE(p1.v_query_budget, 0) - COALESCE(p2.v_query_budget, 0)
            FROM 
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '税利合计' OR code = 'shuilixiangji') p1,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE code = 'lirun_zonge_shui') p2

            UNION ALL

            -- 卷烟销售数量
            SELECT
                '卷烟销售数量',
                COALESCE(p.v_query_actual, 0),
                COALESCE(p.v_compare_actual, 0),
                COALESCE(p.v_query_budget, 0)
            FROM pivot_data p
            WHERE p.indicator_name = '卷烟销售数量' OR p.code = 'juanyan_xiaoshou_shuliang'

            UNION ALL

            -- 卷烟销售收入
            SELECT
                '卷烟销售收入',
                COALESCE(p.v_query_actual, 0),
                COALESCE(p.v_compare_actual, 0),
                COALESCE(p.v_query_budget, 0)
            FROM pivot_data p
            WHERE p.indicator_name = '主营业务收入' OR p.code = 'zhuying_yewu_shouru'

            UNION ALL

            -- 卷烟销售成本
            SELECT
                '卷烟销售成本',
                COALESCE(p.v_query_actual, 0),
                COALESCE(p.v_compare_actual, 0),
                COALESCE(p.v_query_budget, 0)
            FROM pivot_data p
            WHERE p.indicator_name = '主营业务成本' OR p.code = 'zhuying_yewu_chengben'

            UNION ALL

            -- 毛利额 = 收入 - 成本
            SELECT
                '毛利额',
                COALESCE(p1.v_query_actual, 0) - COALESCE(p2.v_query_actual, 0),
                COALESCE(p1.v_compare_actual, 0) - COALESCE(p2.v_compare_actual, 0),
                COALESCE(p1.v_query_budget, 0) - COALESCE(p2.v_query_budget, 0)
            FROM 
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '主营业务收入' OR code = 'zhuying_yewu_shouru') p1,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '主营业务成本' OR code = 'zhuying_yewu_chengben') p2

            UNION ALL

            -- 其他业务收入
            SELECT
                '其他业务收入',
                COALESCE(p.v_query_actual, 0),
                COALESCE(p.v_compare_actual, 0),
                COALESCE(p.v_query_budget, 0)
            FROM pivot_data p
            WHERE p.indicator_name = '其他业务收入' OR p.code = 'qita_yewu_shouru'

            UNION ALL

            -- 其他业务成本
            SELECT
                '其他业务成本',
                COALESCE(p.v_query_actual, 0),
                COALESCE(p.v_compare_actual, 0),
                COALESCE(p.v_query_budget, 0)
            FROM pivot_data p
            WHERE p.indicator_name = '其他业务成本' OR p.code = 'qita_yewu_chengben'

            UNION ALL

            -- 三项费用总额
            SELECT
                '三项费用总额',
                COALESCE(s.v_query_actual, 0) + COALESCE(m.v_query_actual, 0) + COALESCE(r.v_query_actual, 0) + COALESCE(f.v_query_actual, 0),
                COALESCE(s.v_compare_actual, 0) + COALESCE(m.v_compare_actual, 0) + COALESCE(r.v_compare_actual, 0) + COALESCE(f.v_compare_actual, 0),
                COALESCE(s.v_query_budget, 0) + COALESCE(m.v_query_budget, 0) + COALESCE(r.v_query_budget, 0) + COALESCE(f.v_query_budget, 0)
            FROM
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '销售费用') s,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '管理费用') m,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '研发费用') r,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '财务费用') f
        )

        SELECT
            item AS "项目",
            TO_CHAR(ROUND(v_query_actual, 2), 'FM999,999,990.00') AS "${params.queryYear}年${params.startMonth}-${params.endMonth}月",
            TO_CHAR(ROUND(v_compare_actual, 2), 'FM999,999,990.00') AS "${params.compareYear}年${params.startMonth}-${params.endMonth}月",
            CASE 
                WHEN v_compare_actual = 0 THEN 'n/a'
                ELSE TO_CHAR(ROUND((v_query_actual - v_compare_actual) / NULLIF(v_compare_actual, 0) * 100, 2), 'FM999,990.00')
            END AS "同比增减（%）",
            TO_CHAR(ROUND(v_query_budget, 2), 'FM999,999,990.00') AS "${params.queryYear}年预算数",
            CASE 
                WHEN v_query_budget = 0 THEN ''
                ELSE TO_CHAR(ROUND(v_query_actual / NULLIF(v_query_budget, 0) * 100, 2), 'FM999,990.00')
            END AS "预算执行进度（%）"
        FROM (
            SELECT * FROM calculated

            UNION ALL

            -- 毛利率（%）
            SELECT
                '毛利率（%）' AS item,
                ROUND((COALESCE(p1.v_query_actual, 0) - COALESCE(p2.v_query_actual, 0)) / NULLIF(COALESCE(p1.v_query_actual, 0), 0) * 100, 2),
                ROUND((COALESCE(p1.v_compare_actual, 0) - COALESCE(p2.v_compare_actual, 0)) / NULLIF(COALESCE(p1.v_compare_actual, 0), 0) * 100, 2),
                ROUND((COALESCE(p1.v_query_budget, 0) - COALESCE(p2.v_query_budget, 0)) / NULLIF(COALESCE(p1.v_query_budget, 0), 0) * 100, 2)
            FROM 
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '主营业务收入') p1,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '主营业务成本') p2

            UNION ALL

            -- 三项费用率（%）
            SELECT
                '三项费用率（%）' AS item,
                ROUND((COALESCE(s.v_query_actual, 0) + COALESCE(m.v_query_actual, 0) + COALESCE(r.v_query_actual, 0) + COALESCE(f.v_query_actual, 0)) / NULLIF(p1.v_query_actual, 0) * 100, 2),
                ROUND((COALESCE(s.v_compare_actual, 0) + COALESCE(m.v_compare_actual, 0) + COALESCE(r.v_compare_actual, 0) + COALESCE(f.v_compare_actual, 0)) / NULLIF(p1.v_compare_actual, 0) * 100, 2),
                ROUND((COALESCE(s.v_query_budget, 0) + COALESCE(m.v_query_budget, 0) + COALESCE(r.v_query_budget, 0) + COALESCE(f.v_query_budget, 0)) / NULLIF(p1.v_query_budget, 0) * 100, 2)
            FROM
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '主营业务收入') p1,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '销售费用') s,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '管理费用') m,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '研发费用') r,
                (SELECT v_query_actual, v_compare_actual, v_query_budget FROM pivot_data WHERE indicator_name = '财务费用') f
        ) t
        ORDER BY 
            CASE item
                WHEN '税利总额' THEN 1
                WHEN '其中：利润总额' THEN 2
                WHEN '税费（不含所得税）' THEN 3
                WHEN '卷烟销售数量' THEN 4
                WHEN '卷烟销售收入' THEN 5
                WHEN '卷烟销售成本' THEN 6
                WHEN '毛利额' THEN 7
                WHEN '毛利率（%）' THEN 8
                WHEN '其他业务收入' THEN 9
                WHEN '其他业务成本' THEN 10
                WHEN '三项费用总额' THEN 11
                WHEN '三项费用率（%）' THEN 12
            END
    </select>

    <!-- 三项费用分析 -->
    <select id="selectThreeExpenses" resultType="java.util.HashMap">
        WITH base_data AS (
            SELECT
                i.id,
                i.code,
                CASE
                    WHEN i.name = '劳务费用（管理费用）' THEN '劳务费（管理费用）'
                    ELSE i.name
                END AS indicator_name,
                EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) AS year,
                EXTRACT(MONTH FROM TO_DATE(iv.period, 'YYYY-MM')) AS month,
                iv.value
            FROM yjzb_indicator i
            JOIN yjzb_indicator_values iv ON i.id = iv.indicator_id
            WHERE EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) IN (#{params.compareYear}, #{params.queryYear})
              AND EXTRACT(MONTH FROM TO_DATE(iv.period, 'YYYY-MM')) BETWEEN #{params.startMonth} AND #{params.endMonth}
        ),

        -- 汇总销售费用和管理费用的各项明细
        fee_details AS (
            -- 销售费用明细
            SELECT
                CASE
                    WHEN indicator_name LIKE '%（销售费用）' THEN SUBSTRING(indicator_name, 1, POSITION('（' IN indicator_name) - 1)
                    ELSE indicator_name
                END AS item_name,
                'sales' AS fee_type,
                year,
                SUM(value) AS value
            FROM base_data
            WHERE indicator_name LIKE '%（销售费用）'
            GROUP BY
                CASE
                    WHEN indicator_name LIKE '%（销售费用）' THEN SUBSTRING(indicator_name, 1, POSITION('（' IN indicator_name) - 1)
                    ELSE indicator_name
                END,
                year

            UNION ALL

            -- 管理费用明细
            SELECT
                CASE
                    WHEN indicator_name LIKE '%（管理费用）' THEN SUBSTRING(indicator_name, 1, POSITION('（' IN indicator_name) - 1)
                    ELSE indicator_name
                END AS item_name,
                'management' AS fee_type,
                year,
                SUM(value) AS value
            FROM base_data
            WHERE indicator_name LIKE '%（管理费用）'
            GROUP BY
                CASE
                    WHEN indicator_name LIKE '%（管理费用）' THEN SUBSTRING(indicator_name, 1, POSITION('（' IN indicator_name) - 1)
                    ELSE indicator_name
                END,
                year
        ),

        -- 合并销售费用和管理费用的同名项目
        merged_fees AS (
            SELECT
                item_name,
                year,
                SUM(value) AS value
            FROM fee_details
            GROUP BY item_name, year
        ),

        -- 获取财务费用数据
        financial_fee AS (
            SELECT
                '财务费用' AS item_name,
                year,
                SUM(value) AS value
            FROM base_data
            WHERE indicator_name = '财务费用'
            GROUP BY year
        ),

        -- 获取研发费用数据
        rd_fee AS (
            SELECT
                '企业研发费用' AS item_name,
                year,
                SUM(value) AS value
            FROM base_data
            WHERE indicator_name LIKE '%企业研发费用%' and code = 'qiyeyanfa_guanli'
            GROUP BY year
        ),

        -- 合并所有费用项目
        all_fees AS (
            SELECT * FROM merged_fees
            UNION ALL
            SELECT * FROM financial_fee
            UNION ALL
            SELECT * FROM rd_fee
        ),

        -- 透视数据
        pivot_data AS (
            SELECT
                item_name,
                MAX(CASE WHEN year = #{params.queryYear} THEN value END) AS v_2025_actual,
                MAX(CASE WHEN year = #{params.compareYear} THEN value END) AS v_2024_actual
            FROM all_fees
            GROUP BY item_name
        ),

        -- 获取预算数据
        budget_data AS (
            SELECT
                CASE
                    WHEN indicator_name LIKE '%（销售费用）' THEN SUBSTRING(indicator_name, 1, POSITION('（' IN indicator_name) - 1)
                    WHEN indicator_name LIKE '%（管理费用）' THEN SUBSTRING(indicator_name, 1, POSITION('（' IN indicator_name) - 1)
                    ELSE indicator_name
                END AS item_name,
                SUM(value) AS budget_value
            FROM base_data
            WHERE year = #{params.queryYear} AND month = 1
            GROUP BY
                CASE
                    WHEN indicator_name LIKE '%（销售费用）' THEN SUBSTRING(indicator_name, 1, POSITION('（' IN indicator_name) - 1)
                    WHEN indicator_name LIKE '%（管理费用）' THEN SUBSTRING(indicator_name, 1, POSITION('（' IN indicator_name) - 1)
                    ELSE indicator_name
                END
        ),

        -- 合并实际值和预算值
        final_data AS (
            SELECT
                (CASE
                    WHEN p.item_name = '打假经费' THEN '专卖打假经费'
                    WHEN p.item_name = '打私经费' THEN '专卖打私经费'
                    ELSE p.item_name
                END) as item_name,
                p.v_2025_actual,
                p.v_2024_actual,
                COALESCE(p.v_2025_actual, 0) - COALESCE(p.v_2024_actual, 0) AS diff_amount,
                CASE
                    WHEN COALESCE(p.v_2024_actual, 0) = 0 THEN NULL
                    ELSE (COALESCE(p.v_2025_actual, 0) - COALESCE(p.v_2024_actual, 0)) / COALESCE(p.v_2024_actual, 0) * 100
                END AS diff_percent,
                COALESCE(b.budget_value, 0) AS budget_value,
                COALESCE(b.budget_value, 0) - COALESCE(p.v_2025_actual, 0) AS budget_balance,
                CASE
                    WHEN COALESCE(b.budget_value, 0) = 0 THEN NULL
                    ELSE COALESCE(p.v_2025_actual, 0) / COALESCE(b.budget_value, 0) * 100
                END AS budget_execution_rate
            FROM pivot_data p
            LEFT JOIN budget_data b ON p.item_name = b.item_name
            where p.item_name in (
                '修理费', '财务费用', '打假经费', '打私经费', '企业文化建设费', '中介费', '无形资产摊销', '劳务费', '绿化费', '业务招待费', '会议费', '协会会费', '零售终端建设费', '文明吸烟环境建设费', '交易手续费', '长期待摊费用摊销', '政府性基金', '劳动保护费', '书报费', '通讯费', '车杂费', '租赁费', '物业管理费', '保险费', '信息系统维护费', '水电费', '其他', '网络通讯费', '燃料费', '低值易耗品摊销', '包装费', '专卖管理经费', '差旅费', '企业研发费用', '党组织工作经费', '警卫消防费', '折旧费', '办公费', '职工薪酬'
            )
        ),

        -- 计算合计行
        total_row AS (
            SELECT
                '合计' AS item_name,
                SUM(v_2025_actual) AS v_2025_actual,
                SUM(v_2024_actual) AS v_2024_actual,
                SUM(diff_amount) AS diff_amount,
                CASE
                    WHEN SUM(v_2024_actual) = 0 THEN NULL
                    ELSE SUM(diff_amount) / SUM(v_2024_actual) * 100
                END AS diff_percent,
                SUM(budget_value) AS budget_value,
                SUM(budget_balance) AS budget_balance,
                CASE
                    WHEN SUM(budget_value) = 0 THEN NULL
                    ELSE SUM(v_2025_actual) / SUM(budget_value) * 100
                END AS budget_execution_rate
            FROM final_data
        ),

        -- 合并所有数据
        complete_data AS (
            SELECT * FROM total_row
            UNION ALL
            SELECT * FROM final_data
        )

        -- 最终输出
        SELECT
            item_name AS "预算项目",
            CASE
                WHEN item_name = '合计' THEN 2
                ELSE ROW_NUMBER() OVER (ORDER BY diff_amount DESC) + 2
            END AS "行次",
            TO_CHAR(ROUND(v_2025_actual / 10000, 2), 'FM999,999,990.00') AS "${params.queryYear}年${params.startMonth}-${params.endMonth}月",
            TO_CHAR(ROUND(v_2024_actual / 10000, 2), 'FM999,999,990.00') AS "${params.compareYear}年${params.startMonth}-${params.endMonth}月",
            TO_CHAR(ROUND(diff_amount / 10000, 2), 'FM999,999,990.00') AS "增减额",
            CASE
                WHEN diff_percent IS NULL THEN ''
                ELSE TO_CHAR(ROUND(diff_percent, 2), 'FM9990.00')
            END AS "增减比率%",
            TO_CHAR(ROUND(budget_value / 10000, 2), 'FM999,999,990.00') AS "${params.queryYear}年预算数",
            TO_CHAR(ROUND(budget_balance / 10000, 2), 'FM999,999,990.00') AS "预算余额",
            CASE
                WHEN budget_execution_rate IS NULL THEN ''
                ELSE TO_CHAR(ROUND(budget_execution_rate, 2), 'FM9990.00')
            END AS "预算执行率%"
        FROM complete_data
        ORDER BY
            CASE WHEN item_name = '合计' THEN 0 ELSE 1 END,
            diff_amount DESC,
            diff_percent DESC;
    </select>

    <!-- 重点费用支出情况分析 -->
    <select id="selectKeyExpenses" resultType="java.util.HashMap">
        WITH base_data AS (
            SELECT
                i.code,
                i.name AS indicator_name,
                EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) AS year,
                ROUND(SUM(iv.value) / 10000, 2) AS actual_value,
                ROUND(SUM(iv.value) / 10000, 2) AS budget_value
            FROM yjzb_indicator i
            JOIN yjzb_indicator_values iv ON i.id = iv.indicator_id
            WHERE EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) IN (#{params.compareYear}, #{params.queryYear})
              AND EXTRACT(MONTH FROM TO_DATE(iv.period, 'YYYY-MM')) BETWEEN #{params.startMonth} AND #{params.endMonth}
              AND iv.is_deleted = 0
              AND i.is_deleted = 0
            GROUP BY i.code, i.name, EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM'))
        ),

        -- 数据透视，获取2024年和2025年的数据
        pivot_data AS (
            SELECT
                code,
                indicator_name,
                MAX(CASE WHEN year = #{params.queryYear} THEN actual_value END) AS v_2025_actual,
                MAX(CASE WHEN year = #{params.compareYear} THEN actual_value END) AS v_2024_actual,
                MAX(CASE WHEN year = #{params.queryYear} THEN budget_value END) AS v_2025_budget
            FROM base_data
            GROUP BY code, indicator_name
        ),

        -- 计算重点费用项目
        calculated AS (
            -- 两项费用率（%）（销售+管理）
            SELECT
                '两项费用率（%）' AS item,
                ROUND((COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0)) / NULLIF(r.v_2025_actual, 0) * 100, 2) AS v_2025_actual,
                ROUND((COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0)) / NULLIF(r.v_2024_actual, 0) * 100, 2) AS v_2024_actual,
                ROUND((COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0)) / NULLIF(r.v_2025_budget, 0) * 100, 2) AS v_2025_budget,
                1 AS sort_order
            FROM
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '销售费用' OR code = 'xiaoshou_feiyong') s,
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '管理费用' OR code = 'guanli_feiyong') m,
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE indicator_name = '主营业务收入' OR code = 'zhuying_yewu_shouru') r

            UNION ALL

            -- 会议费（销售费用+管理费用）
            SELECT
                '会议费',
                COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
                COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
                COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
                2
            FROM
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'huiyi_xiaoshou') s
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'huiyi_guanli') m ON 1=1

            UNION ALL

            -- 车辆运行费（车辆修理费+车辆保险费+车辆燃油费+车杂费）
            SELECT
                '车辆运行费',
                COALESCE(xr.v_2025_actual, 0) + COALESCE(xb.v_2025_actual, 0) + COALESCE(xf.v_2025_actual, 0) + COALESCE(xz.v_2025_actual, 0) +
                COALESCE(gr.v_2025_actual, 0) + COALESCE(gb.v_2025_actual, 0) + COALESCE(gf.v_2025_actual, 0) + COALESCE(gz.v_2025_actual, 0),
                COALESCE(xr.v_2024_actual, 0) + COALESCE(xb.v_2024_actual, 0) + COALESCE(xf.v_2024_actual, 0) + COALESCE(xz.v_2024_actual, 0) +
                COALESCE(gr.v_2024_actual, 0) + COALESCE(gb.v_2024_actual, 0) + COALESCE(gf.v_2024_actual, 0) + COALESCE(gz.v_2024_actual, 0),
                COALESCE(xr.v_2025_budget, 0) + COALESCE(xb.v_2025_budget, 0) + COALESCE(xf.v_2025_budget, 0) + COALESCE(xz.v_2025_budget, 0) +
                COALESCE(gr.v_2025_budget, 0) + COALESCE(gb.v_2025_budget, 0) + COALESCE(gf.v_2025_budget, 0) + COALESCE(gz.v_2025_budget, 0),
                3
            FROM
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'xiuli_cheliang_xiaoshou') xr
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'baoxian_cheliang_xiaoshou') xb ON 1=1
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'ranliao_cheliang_xiaoshou') xf ON 1=1
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'chezha_xiaoshou') xz ON 1=1
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'xiuli_cheliang_guanli') gr ON 1=1
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'baoxian_cheliang_guanli') gb ON 1=1
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'ranliao_cheliang_guanli') gf ON 1=1
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'chezha_guanli') gz ON 1=1

            UNION ALL

            -- （一）车辆修理费
            SELECT
                '（一）车辆修理费',
                COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
                COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
                COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
                4
            FROM
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'xiuli_cheliang_xiaoshou') s
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'xiuli_cheliang_guanli') m ON 1=1

            UNION ALL

            -- （二）车辆保险费
            SELECT
                '（二）车辆保险费',
                COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
                COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
                COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
                5
            FROM
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'baoxian_cheliang_xiaoshou') s
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'baoxian_cheliang_guanli') m ON 1=1

            UNION ALL

            -- （三）车辆燃油费
            SELECT
                '（三）车辆燃油费',
                COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
                COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
                COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
                6
            FROM
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'ranliao_cheliang_xiaoshou') s
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'ranliao_cheliang_guanli') m ON 1=1

            UNION ALL

            -- （四）车杂费
            SELECT
                '（四）车杂费',
                COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
                COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
                COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
                7
            FROM
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'chezha_xiaoshou') s
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'chezha_guanli') m ON 1=1

            UNION ALL

            -- 福利费（销售费用+管理费用中的员工福利）
            SELECT
                '福利费',
                COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
                COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
                COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
                8
            FROM
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_duanqi_yuangongfuli_xiaoshou') s
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_duanqi_yuangongfuli_guanli') m ON 1=1

            UNION ALL

            -- （一）在职人员福利费支出
            SELECT
                '（一）在职人员福利费支出',
                COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
                COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
                COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
                9
            FROM
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_duanqi_yuangongfuli_xiaoshou') s
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_duanqi_yuangongfuli_guanli') m ON 1=1

            UNION ALL

            -- （二）离退休人员福利费支出
            SELECT
                '（二）离退休人员福利费支出',
                COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
                COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
                COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
                10
            FROM
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_lizhi_xiaoshou') s
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_lizhi_guanli') m ON 1=1

            UNION ALL

            -- 实发工资（销售费用+管理费用中的工资）
            SELECT
                '实发工资',
                COALESCE(s.v_2025_actual, 0) + COALESCE(m.v_2025_actual, 0),
                COALESCE(s.v_2024_actual, 0) + COALESCE(m.v_2024_actual, 0),
                COALESCE(s.v_2025_budget, 0) + COALESCE(m.v_2025_budget, 0),
                11
            FROM
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_duanqi_gongzi_xiaoshou') s
            FULL OUTER JOIN
                (SELECT v_2025_actual, v_2024_actual, v_2025_budget FROM pivot_data WHERE code = 'zhigongxinchou_duanqi_gongzi_guanli') m ON 1=1

            UNION ALL

            -- 零售终端建设费
            SELECT
                '零售终端建设费',
                COALESCE(p.v_2025_actual, 0),
                COALESCE(p.v_2024_actual, 0),
                COALESCE(p.v_2025_budget, 0),
                12
            FROM pivot_data p
            WHERE p.code = 'lingshou_zhongduan_xiaoshou'

            UNION ALL

            -- （一）终端形象提升
            SELECT
                '（一）终端形象提升',
                0, -- 需要根据实际数据调整
                0,
                0,
                13

            UNION ALL

            -- （二）终端信息化建设
            SELECT
                '（二）终端信息化建设',
                0,
                0,
                0,
                14

            UNION ALL

            -- （三）消费体验区建设
            SELECT
                '（三）消费体验区建设',
                0,
                0,
                0,
                15

            UNION ALL

            -- （四）零售客户培训
            SELECT
                '（四）零售客户培训',
                0,
                0,
                0,
                16

            UNION ALL

            -- 文明吸烟环境建设费
            SELECT
                '文明吸烟环境建设费',
                COALESCE(p.v_2025_actual, 0),
                COALESCE(p.v_2024_actual, 0),
                COALESCE(p.v_2025_budget, 0),
                17
            FROM pivot_data p
            WHERE p.code = 'wenming_xiyan_guanli'

            UNION ALL

            -- 研发费
            SELECT
                '研发费',
                COALESCE(p.v_2025_actual, 0),
                COALESCE(p.v_2024_actual, 0),
                COALESCE(p.v_2025_budget, 0),
                18
            FROM pivot_data p
            WHERE p.code = 'qiyeyanfa_guanli'

            UNION ALL

            -- 对外捐赠支出
            SELECT
                '对外捐赠支出',
                0, -- 需要根据实际数据调整
                0,
                0,
                19

            UNION ALL

            -- （一）扶贫捐赠支出
            SELECT
                '（一）扶贫捐赠支出',
                0,
                0,
                0,
                20

            UNION ALL

            -- （二）水源工程支出
            SELECT
                '（二）水源工程支出',
                0,
                0,
                0,
                21
        )

        -- 最终输出，包含福利费占实发工资比例的计算
        SELECT * FROM (
            SELECT
                item AS "项目",
                CASE
                    WHEN item = '两项费用率（%）' THEN TO_CHAR(v_2025_actual, 'FM990.00')
                    ELSE TO_CHAR(ROUND(v_2025_actual, 2), 'FM999,999,990.00')
                END AS "${params.queryYear}年${params.startMonth}-${params.endMonth}月",
                CASE
                    WHEN item = '两项费用率（%）' THEN TO_CHAR(v_2024_actual, 'FM990.00')
                    ELSE TO_CHAR(ROUND(v_2024_actual, 2), 'FM999,999,990.00')
                END AS "${params.compareYear}年${params.startMonth}-${params.endMonth}月",
                CASE
                    WHEN v_2024_actual = 0 AND item != '两项费用率（%）' THEN 'n/a'
                    WHEN item = '两项费用率（%）' THEN TO_CHAR(ROUND(v_2025_actual - v_2024_actual, 2), 'FM990.00')
                    ELSE TO_CHAR(ROUND((v_2025_actual - v_2024_actual) / NULLIF(v_2024_actual, 0) * 100, 2), 'FM990.00')
                END AS "同比增减（%）",
                CASE
                    WHEN item = '两项费用率（%）' THEN TO_CHAR(v_2025_budget, 'FM990.00')
                    ELSE TO_CHAR(ROUND(v_2025_budget, 2), 'FM999,999,990.00')
                END AS "${params.queryYear}年预算数",
                CASE
                    WHEN v_2025_budget = 0 OR item = '两项费用率（%）' THEN '-'
                    ELSE TO_CHAR(ROUND(v_2025_actual / NULLIF(v_2025_budget, 0) * 100, 2), 'FM990.00')
                END AS "执行进度（%）",
                sort_order
            FROM calculated

            UNION ALL

            -- 福利费占实发工资比例（%）
            SELECT
                '福利费占实发工资比例（%）' AS "项目",
                TO_CHAR(ROUND(
                    (SELECT v_2025_actual FROM calculated WHERE item = '福利费') /
                    NULLIF((SELECT v_2025_actual FROM calculated WHERE item = '实发工资'), 0) * 100, 2
                ), 'FM990.00') AS "${params.queryYear}年${params.startMonth}-${params.endMonth}月",
                TO_CHAR(ROUND(
                    (SELECT v_2024_actual FROM calculated WHERE item = '福利费') /
                    NULLIF((SELECT v_2024_actual FROM calculated WHERE item = '实发工资'), 0) * 100, 2
                ), 'FM990.00') AS "${params.compareYear}年${params.startMonth}-${params.endMonth}月",
                TO_CHAR(ROUND(
                    ((SELECT v_2025_actual FROM calculated WHERE item = '福利费') /
                     NULLIF((SELECT v_2025_actual FROM calculated WHERE item = '实发工资'), 0) * 100) -
                    ((SELECT v_2024_actual FROM calculated WHERE item = '福利费') /
                     NULLIF((SELECT v_2024_actual FROM calculated WHERE item = '实发工资'), 0) * 100), 2
                ), 'FM990.00') AS "同比增减（%）",
                TO_CHAR(ROUND(
                    (SELECT v_2025_budget FROM calculated WHERE item = '福利费') /
                    NULLIF((SELECT v_2025_budget FROM calculated WHERE item = '实发工资'), 0) * 100, 2
                ), 'FM990.00') AS "${params.queryYear}年预算数",
                TO_CHAR(ROUND(
                    ((SELECT v_2025_actual FROM calculated WHERE item = '福利费') /
                     NULLIF((SELECT v_2025_actual FROM calculated WHERE item = '实发工资'), 0) * 100) /
                    NULLIF(((SELECT v_2025_budget FROM calculated WHERE item = '福利费') /
                            NULLIF((SELECT v_2025_budget FROM calculated WHERE item = '实发工资'), 0) * 100), 0) * 100, 2
                ), 'FM990.00') AS "执行进度（%）",
                12 AS sort_order
        ) final_result
        ORDER BY sort_order
    </select>

</mapper>